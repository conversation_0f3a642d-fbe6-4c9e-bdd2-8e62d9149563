<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.ymyacc.cloud</groupId>
  <artifactId>ymyacc-dependencies</artifactId>
  <version>V1.0.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>ymyacc-dependencies</name>
  <description>基础 bom 文件，管理整个项目的依赖版本</description>
  <url>https://github.com/YunaiV/ruoyi-vue-pro</url>
  <properties>
    <tika-core.version>3.2.2</tika-core.version>
    <podam.version>8.0.2.RELEASE</podam.version>
    <flatten-maven-plugin.version>1.7.2</flatten-maven-plugin.version>
    <opengauss.jdbc.version>5.1.0</opengauss.jdbc.version>
    <mapstruct.version>1.6.3</mapstruct.version>
    <fastjson.version>1.2.83</fastjson.version>
    <weixin-java.version>4.7.7-20250808.182223</weixin-java.version>
    <mybatis.version>3.5.19</mybatis.version>
    <opentracing.version>0.33.0</opentracing.version>
    <kingbase.jdbc.version>8.6.0</kingbase.jdbc.version>
    <rocketmq-spring.version>2.3.4</rocketmq-spring.version>
    <ip2region.version>2.7.0</ip2region.version>
    <awssdk.version>2.30.14</awssdk.version>
    <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
    <redisson.version>3.51.0</redisson.version>
    <pf4j-spring.version>0.9.0</pf4j-spring.version>
    <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
    <mqtt.version>1.2.5</mqtt.version>
    <vertx.version>4.5.13</vertx.version>
    <spring.boot.version>3.5.5</spring.boot.version>
    <springdoc.version>2.8.11</springdoc.version>
    <lock4j.version>2.2.7</lock4j.version>
    <hutool-6.version>6.0.0-M22</hutool-6.version>
    <jsoup.version>1.21.2</jsoup.version>
    <mybatis-plus.version>3.5.12</mybatis-plus.version>
    <fastexcel.version>1.3.0</fastexcel.version>
    <knife4j.version>4.5.0</knife4j.version>
    <lombok.version>1.18.38</lombok.version>
    <flowable.version>7.0.1</flowable.version>
    <skywalking.version>9.5.0</skywalking.version>
    <mockito-inline.version>5.2.0</mockito-inline.version>
    <justauth.version>1.16.7</justauth.version>
    <velocity.version>2.4.1</velocity.version>
    <reflections.version>0.10.2</reflections.version>
    <bizlog-sdk.version>3.0.6</bizlog-sdk.version>
    <commons-lang3.version>3.18.0</commons-lang3.version>
    <mybatis-plus-join.version>1.5.4</mybatis-plus-join.version>
    <hutool-5.version>5.8.40</hutool-5.version>
    <revision>V1.0.0-SNAPSHOT</revision>
    <jsch.version>0.1.55</jsch.version>
    <justauth-starter.version>1.4.0</justauth-starter.version>
    <xxl-job.version>2.4.0</xxl-job.version>
    <spring-boot-admin.version>3.5.2</spring-boot-admin.version>
    <netty.version>4.2.4.Final</netty.version>
    <jedis-mock.version>1.1.11</jedis-mock.version>
    <guava.version>33.4.8-jre</guava.version>
    <jimubi.version>2.1.0</jimubi.version>
    <spring.cloud.version>2025.0.0</spring.cloud.version>
    <anji-plus-captcha.version>1.4.0</anji-plus-captcha.version>
    <spring.cloud.alibaba.version>2023.0.3.3</spring.cloud.alibaba.version>
    <taos.version>3.7.3</taos.version>
    <commons-net.version>3.11.1</commons-net.version>
    <jimureport.version>2.1.1</jimureport.version>
    <druid.version>1.2.27</druid.version>
    <easy-trans.version>3.0.6</easy-trans.version>
    <dm8.jdbc.version>8.1.3.140</dm8.jdbc.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-bom</artifactId>
        <version>${netty.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring.boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring.cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-dependencies</artifactId>
        <version>${spring.cloud.alibaba.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.github.mouzt</groupId>
        <artifactId>bizlog-sdk</artifactId>
        <version>${bizlog-sdk.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-processor</artifactId>
        <version>${spring.boot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        <version>${knife4j.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        <version>${springdoc.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-gateway-spring-boot-starter</artifactId>
        <version>${knife4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-3-starter</artifactId>
        <version>${druid.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis</artifactId>
        <version>${mybatis.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-jsqlparser</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-generator</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
        <version>${dynamic-datasource.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.yulichang</groupId>
        <artifactId>mybatis-plus-join-boot-starter</artifactId>
        <version>${mybatis-plus-join.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fhs-opensource</groupId>
        <artifactId>easy-trans-spring-boot-starter</artifactId>
        <version>${easy-trans.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.fhs-opensource</groupId>
        <artifactId>easy-trans-mybatis-plus-extend</artifactId>
        <version>${easy-trans.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fhs-opensource</groupId>
        <artifactId>easy-trans-anno</artifactId>
        <version>${easy-trans.version}</version>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson-spring-boot-starter</artifactId>
        <version>${redisson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.dameng</groupId>
        <artifactId>DmJdbcDriver18</artifactId>
        <version>${dm8.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>org.opengauss</groupId>
        <artifactId>opengauss-jdbc</artifactId>
        <version>${opengauss.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.kingbase</groupId>
        <artifactId>kingbase8</artifactId>
        <version>${kingbase.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>com.taosdata.jdbc</groupId>
        <artifactId>taos-jdbcdriver</artifactId>
        <version>${taos.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        <version>${spring.cloud.alibaba.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>logback-adapter</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.xuxueli</groupId>
        <artifactId>xxl-job-core</artifactId>
        <version>${xxl-job.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.rocketmq</groupId>
        <artifactId>rocketmq-spring-boot-starter</artifactId>
        <version>${rocketmq-spring.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
        <version>${lock4j.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-trace</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-logback-1.x</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-opentracing</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-api</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-util</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-noop</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-client</artifactId>
        <version>${spring-boot-admin.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <version>${mockito-inline.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>${spring.boot.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.github.fppt</groupId>
        <artifactId>jedis-mock</artifactId>
        <version>${jedis-mock.version}</version>
      </dependency>
      <dependency>
        <groupId>uk.co.jemos.podam</groupId>
        <artifactId>podam</artifactId>
        <version>${podam.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-spring-boot-starter-process</artifactId>
        <version>${flowable.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-spring-boot-starter-actuator</artifactId>
        <version>${flowable.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ymyacc.cloud</groupId>
        <artifactId>ymyacc-common</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-jdk8</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-processor</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool-5.version}</version>
      </dependency>
      <dependency>
        <groupId>org.dromara.hutool</groupId>
        <artifactId>hutool-extra</artifactId>
        <version>${hutool-6.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.idev.excel</groupId>
        <artifactId>fastexcel</artifactId>
        <version>${fastexcel.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tika</groupId>
        <artifactId>tika-core</artifactId>
        <version>${tika-core.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity-engine-core</artifactId>
        <version>${velocity.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>transmittable-thread-local</artifactId>
        <version>${transmittable-thread-local.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-net</groupId>
        <artifactId>commons-net</artifactId>
        <version>${commons-net.version}</version>
      </dependency>
      <dependency>
        <groupId>com.jcraft</groupId>
        <artifactId>jsch</artifactId>
        <version>${jsch.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons-lang3.version}</version>
      </dependency>
      <dependency>
        <groupId>com.anji-plus</groupId>
        <artifactId>captcha-spring-boot-starter</artifactId>
        <version>${anji-plus-captcha.version}</version>
      </dependency>
      <dependency>
        <groupId>org.lionsoul</groupId>
        <artifactId>ip2region</artifactId>
        <version>${ip2region.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jsoup</groupId>
        <artifactId>jsoup</artifactId>
        <version>${jsoup.version}</version>
      </dependency>
      <dependency>
        <groupId>org.reflections</groupId>
        <artifactId>reflections</artifactId>
        <version>${reflections.version}</version>
      </dependency>
      <dependency>
        <groupId>software.amazon.awssdk</groupId>
        <artifactId>s3</artifactId>
        <version>${awssdk.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>weixin-java-pay</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>wx-java-mp-spring-boot-starter</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>me.zhyd.oauth</groupId>
        <artifactId>JustAuth</artifactId>
        <version>${justauth.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xkcoding.justauth</groupId>
        <artifactId>justauth-spring-boot-starter</artifactId>
        <version>${justauth-starter.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jeecgframework.jimureport</groupId>
        <artifactId>jimureport-spring-boot3-starter-fastjson2</artifactId>
        <version>${jimureport.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jeecgframework.jimureport</groupId>
        <artifactId>jimubi-spring-boot3-starter</artifactId>
        <version>${jimubi.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
          </exclusion>
          <exclusion>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.pf4j</groupId>
        <artifactId>pf4j-spring</artifactId>
        <version>${pf4j-spring.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.vertx</groupId>
        <artifactId>vertx-core</artifactId>
        <version>${vertx.version}</version>
      </dependency>
      <dependency>
        <groupId>io.vertx</groupId>
        <artifactId>vertx-web</artifactId>
        <version>${vertx.version}</version>
      </dependency>
      <dependency>
        <groupId>io.vertx</groupId>
        <artifactId>vertx-mqtt</artifactId>
        <version>${vertx.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
